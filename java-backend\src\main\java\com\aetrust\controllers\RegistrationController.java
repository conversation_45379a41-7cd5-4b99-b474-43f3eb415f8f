package com.aetrust.controllers;

import com.aetrust.dto.RequestDTOs.*;
import com.aetrust.dto.ApiResponse;
import com.aetrust.services.RegistrationService;
import com.aetrust.services.SecurityService;
import com.aetrust.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.Map;
import java.util.HashMap;

@Slf4j
@RestController
@RequestMapping("/auth/register")
@Validated
public class RegistrationController {
    
    @Autowired
    private RegistrationService registrationService;
    
    @Autowired
    private SecurityService securityService;
    
    @PostMapping("/initiate")
    public ResponseEntity<ApiResponse<Map<String, Object>>> initiateRegistration(
            @Valid @RequestBody RegistrationInitRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            // rate limiting
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                ipAddress, "ip", "/auth/register/initiate");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("rate limit exceeded", "RATE_LIMIT_EXCEEDED"));
            }
            
            // suspicious activity check
            SecurityService.ActivityContext activity = new SecurityService.ActivityContext(
                ipAddress, userAgent, System.currentTimeMillis());
            
            SecurityService.SuspiciousActivityResult suspiciousResult = 
                securityService.detectSuspiciousActivity("anonymous", activity);
            
            if (suspiciousResult.getRiskScore() > 50) {
                return ResponseEntity.status(403).body(
                    ApiResponse.error("registration blocked due to suspicious activity", "SUSPICIOUS_ACTIVITY"));
            }
            
            // initiate registration
            RegistrationService.RegistrationResult result = registrationService.initiateRegistration(request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("registrationId", result.getRegistrationId());
            responseData.put("nextStep", result.getNextStep());
            responseData.put("phoneVerificationRequired", true);
            responseData.put("expiresIn", 900); // 15 minutes
            
            return ResponseEntity.ok(
                ApiResponse.success("registration initiated successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error initiating registration: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("registration initiation failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/verify-phone")
    public ResponseEntity<ApiResponse<Map<String, Object>>> verifyPhone(
            @Valid @RequestBody PhoneVerificationRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = getClientIp(httpRequest);
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getPhone(), "user", "/auth/register/verify-phone");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many verification attempts", "RATE_LIMIT_EXCEEDED"));
            }
            
            RegistrationService.VerificationResult result = registrationService.verifyPhone(request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("phoneVerified", true);
            responseData.put("nextStep", result.getNextStep());
            responseData.put("registrationId", result.getRegistrationId());
            
            if (result.isCompleted()) {
                responseData.put("accessToken", result.getAccessToken());
                responseData.put("refreshToken", result.getRefreshToken());
                responseData.put("userProfile", result.getUserProfile());
            }
            
            return ResponseEntity.ok(
                ApiResponse.success("phone verification successful", responseData));
                
        } catch (Exception error) {
            log.error("Error verifying phone: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("phone verification failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/verify-email")
    public ResponseEntity<ApiResponse<Map<String, Object>>> verifyEmail(
            @Valid @RequestBody EmailVerificationRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = getClientIp(httpRequest);
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getEmail(), "user", "/auth/register/verify-email");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many verification attempts", "RATE_LIMIT_EXCEEDED"));
            }
            
            RegistrationService.VerificationResult result = registrationService.verifyEmail(request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("emailVerified", true);
            responseData.put("nextStep", result.getNextStep());
            responseData.put("registrationId", result.getRegistrationId());
            
            return ResponseEntity.ok(
                ApiResponse.success("email verification successful", responseData));
                
        } catch (Exception error) {
            log.error("Error verifying email: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("email verification failed", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/complete")
    public ResponseEntity<ApiResponse<Map<String, Object>>> completeRegistration(
            @Valid @RequestBody RegistrationCompleteRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = getClientIp(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            RegistrationService.CompletionResult result = registrationService.completeRegistration(request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("registrationCompleted", true);
            responseData.put("accessToken", result.getAccessToken());
            responseData.put("refreshToken", result.getRefreshToken());
            responseData.put("userProfile", result.getUserProfile());
            responseData.put("walletCreated", result.isWalletCreated());
            
            log.info("Registration completed successfully for user: {}", 
                securityService.maskSensitiveData(result.getUserProfile().get("email").toString()));
            
            return ResponseEntity.ok(
                ApiResponse.success("registration completed successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error completing registration: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("registration completion failed", "INTERNAL_ERROR"));
        }
    }
    
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRegistrationStatus(
            @RequestParam String phone,
            HttpServletRequest httpRequest) {
        
        try {
            RegistrationService.StatusResult result = registrationService.getRegistrationStatus(phone);
            
            if (!result.isFound()) {
                return ResponseEntity.notFound().build();
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("registrationExists", true);
            responseData.put("currentStep", result.getCurrentStep());
            responseData.put("phoneVerified", result.isPhoneVerified());
            responseData.put("emailVerified", result.isEmailVerified());
            responseData.put("isCompleted", result.isCompleted());
            responseData.put("expiresAt", result.getExpiresAt());
            
            return ResponseEntity.ok(
                ApiResponse.success("registration status retrieved", responseData));
                
        } catch (Exception error) {
            log.error("Error getting registration status: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to get registration status", "INTERNAL_ERROR"));
        }
    }
    
    @PostMapping("/resend-code")
    public ResponseEntity<ApiResponse<Map<String, Object>>> resendVerificationCode(
            @Valid @RequestBody ResendCodeRequest request,
            HttpServletRequest httpRequest) {
        
        try {
            String ipAddress = getClientIp(httpRequest);
            
            SecurityService.RateLimitResult rateLimit = securityService.checkRateLimit(
                request.getPhone(), "user", "/auth/register/resend-code");
            
            if (!rateLimit.isAllowed()) {
                return ResponseEntity.status(429).body(
                    ApiResponse.error("too many resend attempts", "RATE_LIMIT_EXCEEDED"));
            }
            
            RegistrationService.ResendResult result = registrationService.resendVerificationCode(request);
            
            if (!result.isSuccess()) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error(result.getMessage(), result.getErrorCode()));
            }
            
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("codeSent", true);
            responseData.put("codeType", result.getCodeType());
            responseData.put("expiresIn", 300); // 5 minutes
            
            return ResponseEntity.ok(
                ApiResponse.success("verification code sent successfully", responseData));
                
        } catch (Exception error) {
            log.error("Error resending verification code: {}", error.getMessage());
            return ResponseEntity.status(500).body(
                ApiResponse.error("failed to resend verification code", "INTERNAL_ERROR"));
        }
    }
    
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
