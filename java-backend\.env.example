# AeTrust Java Backend Environment Configuration

# Server Configuration
PORT=3000
SPRING_PROFILES_ACTIVE=development

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/aetrust
MONGODB_DATABASE=aetrust
DB_CONNECTION_POOL_SIZE=10
DB_CONNECTION_TIMEOUT=30000

# Redis Configuration (Cache System)
# Set REDIS_ENABLED=true to use Redis, false to use in-memory cache
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_POOL_MAX_ACTIVE=10
REDIS_POOL_MAX_IDLE=10
REDIS_POOL_MIN_IDLE=1
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-secure
JWT_REFRESH_SECRET=your-super-secret-refresh-key-here-make-it-long-and-secure
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Encryption Configuration
ENCRYPTION_KEY=your-32-character-encryption-key-here
HASH_SALT_ROUNDS=12

# Security Configuration
SECURITY_MAX_LOGIN_ATTEMPTS=5
SECURITY_LOCKOUT_DURATION=900000
SECURITY_RATE_LIMIT_WINDOW=3600000

# Notification Configuration
SMS_PROVIDER=twilio
EMAIL_PROVIDER=sendgrid
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=5242880

# Logging Configuration
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_AETRUST=DEBUG
LOGGING_FILE=logs/aetrust-backend.log

# Development/Production Flags
DEBUG_MODE=true
ENABLE_SWAGGER=true
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Cache Configuration
CACHE_DEFAULT_TTL=3600
CONFIG_CACHE_TTL=300

# Database Migration
FLYWAY_ENABLED=false

# Monitoring & Health Checks
MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE=health,info,metrics
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=when_authorized

# External API Configuration
EXTERNAL_API_TIMEOUT=30000
EXTERNAL_API_RETRY_ATTEMPTS=3
EXTERNAL_API_CIRCUIT_BREAKER_ENABLED=true

# Performance Configuration
THREAD_POOL_SIZE=20
CONNECTION_TIMEOUT=30000
READ_TIMEOUT=45000

# Security Headers
SECURITY_HEADERS_ENABLED=true
CSRF_PROTECTION_ENABLED=true
XSS_PROTECTION_ENABLED=true

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_CAPACITY=200

# Session Configuration
SESSION_TIMEOUT=1800000
SESSION_CLEANUP_INTERVAL=300000

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
