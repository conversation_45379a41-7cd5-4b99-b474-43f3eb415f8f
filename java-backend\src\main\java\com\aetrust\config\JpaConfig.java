package com.aetrust.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Slf4j
@Configuration
@EnableJpaRepositories(basePackages = "com.aetrust.repositories")
@EnableJpaAuditing
@EnableTransactionManagement
public class JpaConfig {
    
    public JpaConfig() {
        log.info("JPA configuration initialized for PostgreSQL");
    }
}
